import { EventEmitter } from 'events';
import {
  DownloadTask,
  TaskStatus,
  CourseResource
} from '../types';

/**
 * 队列配置
 */
export interface QueueConfig {
  maxConcurrentTasks: number;
  enablePriority: boolean;
  autoStart: boolean;
  persistState: boolean;
  maxRetries: number;
  retryDelay: number;
}

/**
 * 任务优先级
 */
export enum TaskPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  URGENT = 4
}

/**
 * 队列项
 */
export interface QueueItem {
  taskId: string;
  priority: TaskPriority;
  addedAt: Date;
  retryCount: number;
  lastRetryAt?: Date;
}

/**
 * 队列统计信息
 */
export interface QueueStats {
  totalItems: number;
  pendingItems: number;
  activeItems: number;
  completedItems: number;
  failedItems: number;
  averageWaitTime: number;
  throughput: number; // 每分钟处理的任务数
}

/**
 * 队列状态
 */
export type QueueStatus = 'idle' | 'running' | 'paused' | 'stopped';

/**
 * 队列事件
 */
export interface QueueEvents {
  'queue-started': () => void;
  'queue-paused': () => void;
  'queue-stopped': () => void;
  'queue-empty': () => void;
  'item-added': (item: QueueItem) => void;
  'item-removed': (taskId: string) => void;
  'item-processing': (taskId: string) => void;
  'item-completed': (taskId: string) => void;
  'item-failed': (taskId: string, error: string) => void;
  'stats-updated': (stats: QueueStats) => void;
  'priority-changed': (taskId: string, oldPriority: TaskPriority, newPriority: TaskPriority) => void;
}

/**
 * 下载队列管理器
 * 负责管理下载任务的队列、优先级和调度
 */
export class DownloadQueue extends EventEmitter {
  private config: QueueConfig;
  private queue: QueueItem[] = [];
  private activeItems: Set<string> = new Set();
  private completedItems: Set<string> = new Set();
  private failedItems: Map<string, string> = new Map(); // taskId -> error message
  private status: QueueStatus = 'idle';
  private isProcessing: boolean = false;
  private processingStartTime?: Date;
  private completedCount: number = 0;
  private lastThroughputCalculation: Date = new Date();

  private readonly defaultConfig: QueueConfig = {
    maxConcurrentTasks: 3,
    enablePriority: true,
    autoStart: true,
    persistState: false,
    maxRetries: 3,
    retryDelay: 5000
  };

  constructor(config?: Partial<QueueConfig>) {
    super();
    this.config = { ...this.defaultConfig, ...config };
  }

  /**
   * 添加任务到队列
   */
  addTask(taskId: string, priority: TaskPriority = TaskPriority.NORMAL): QueueItem {
    // 检查任务是否已存在
    const existingItem = this.queue.find(item => item.taskId === taskId);
    if (existingItem) {
      throw new Error(`任务 ${taskId} 已在队列中`);
    }

    // 检查是否在活动任务中
    if (this.activeItems.has(taskId)) {
      throw new Error(`任务 ${taskId} 正在处理中`);
    }

    const queueItem: QueueItem = {
      taskId,
      priority,
      addedAt: new Date(),
      retryCount: 0
    };

    // 根据优先级插入队列
    if (this.config.enablePriority) {
      this.insertByPriority(queueItem);
    } else {
      this.queue.push(queueItem);
    }

    this.emit('item-added', queueItem);
    this.emitStatsUpdate();

    // 如果启用自动开始，则开始处理队列
    if (this.config.autoStart && this.status === 'idle') {
      this.start();
    } else if (this.status === 'running') {
      this.processNext();
    }

    return queueItem;
  }

  /**
   * 批量添加任务
   */
  addBatchTasks(taskIds: string[], priority: TaskPriority = TaskPriority.NORMAL): QueueItem[] {
    const items: QueueItem[] = [];
    
    for (const taskId of taskIds) {
      try {
        const item = this.addTask(taskId, priority);
        items.push(item);
      } catch (error) {
        console.warn(`添加任务 ${taskId} 失败:`, error);
      }
    }

    return items;
  }

  /**
   * 移除队列中的任务
   */
  removeTask(taskId: string): boolean {
    const index = this.queue.findIndex(item => item.taskId === taskId);
    if (index === -1) {
      return false;
    }

    this.queue.splice(index, 1);
    this.emit('item-removed', taskId);
    this.emitStatsUpdate();
    return true;
  }

  /**
   * 更改任务优先级
   */
  changePriority(taskId: string, newPriority: TaskPriority): boolean {
    const index = this.queue.findIndex(item => item.taskId === taskId);
    if (index === -1) {
      return false;
    }

    const item = this.queue[index];
    const oldPriority = item.priority;
    
    // 移除原位置的任务
    this.queue.splice(index, 1);
    
    // 更新优先级并重新插入
    item.priority = newPriority;
    this.insertByPriority(item);

    this.emit('priority-changed', taskId, oldPriority, newPriority);
    this.emitStatsUpdate();
    return true;
  }

  /**
   * 开始处理队列
   */
  start(): void {
    if (this.status === 'running') {
      return;
    }

    this.status = 'running';
    this.processingStartTime = new Date();
    this.emit('queue-started');
    this.processNext();
  }

  /**
   * 暂停队列处理
   */
  pause(): void {
    if (this.status !== 'running') {
      return;
    }

    this.status = 'paused';
    this.emit('queue-paused');
  }

  /**
   * 停止队列处理
   */
  stop(): void {
    this.status = 'stopped';
    this.isProcessing = false;
    this.emit('queue-stopped');
  }

  /**
   * 恢复队列处理
   */
  resume(): void {
    if (this.status !== 'paused') {
      return;
    }

    this.status = 'running';
    this.processNext();
  }

  /**
   * 清空队列
   */
  clear(): void {
    const removedItems = [...this.queue];
    this.queue = [];
    
    removedItems.forEach(item => {
      this.emit('item-removed', item.taskId);
    });
    
    this.emitStatsUpdate();
  }

  /**
   * 获取队列中的下一个任务
   */
  getNextTask(): string | null {
    if (this.queue.length === 0 || this.activeItems.size >= this.config.maxConcurrentTasks) {
      return null;
    }

    const nextItem = this.queue[0];
    return nextItem ? nextItem.taskId : null;
  }

  /**
   * 标记任务开始处理
   */
  markTaskStarted(taskId: string): boolean {
    const index = this.queue.findIndex(item => item.taskId === taskId);
    if (index === -1) {
      return false;
    }

    // 从队列中移除并添加到活动任务
    this.queue.splice(index, 1);
    this.activeItems.add(taskId);
    
    this.emit('item-processing', taskId);
    this.emitStatsUpdate();
    return true;
  }

  /**
   * 标记任务完成
   */
  markTaskCompleted(taskId: string): boolean {
    if (!this.activeItems.has(taskId)) {
      return false;
    }

    this.activeItems.delete(taskId);
    this.completedItems.add(taskId);
    this.completedCount++;
    
    this.emit('item-completed', taskId);
    this.emitStatsUpdate();
    
    // 继续处理下一个任务
    this.processNext();
    return true;
  }

  /**
   * 标记任务失败
   */
  markTaskFailed(taskId: string, error: string): boolean {
    if (!this.activeItems.has(taskId)) {
      return false;
    }

    this.activeItems.delete(taskId);

    // 查找原始队列项以检查重试次数
    const originalItem = this.queue.find(item => item.taskId === taskId);
    const retryCount = originalItem ? originalItem.retryCount : 0;

    if (retryCount < this.config.maxRetries) {
      // 重新加入队列进行重试
      const retryItem: QueueItem = {
        taskId,
        priority: originalItem?.priority || TaskPriority.NORMAL,
        addedAt: originalItem?.addedAt || new Date(),
        retryCount: retryCount + 1,
        lastRetryAt: new Date()
      };

      // 延迟后重新加入队列
      setTimeout(() => {
        if (this.config.enablePriority) {
          this.insertByPriority(retryItem);
        } else {
          this.queue.push(retryItem);
        }
        this.processNext();
      }, this.config.retryDelay);
    } else {
      // 超过最大重试次数，标记为失败
      this.failedItems.set(taskId, error);
      this.emit('item-failed', taskId, error);
    }

    this.emitStatsUpdate();
    this.processNext();
    return true;
  }

  /**
   * 获取队列统计信息
   */
  getStats(): QueueStats {
    const now = new Date();
    const totalItems = this.queue.length + this.activeItems.size + this.completedItems.size + this.failedItems.size;

    // 计算平均等待时间
    const averageWaitTime = this.calculateAverageWaitTime();

    // 计算吞吐量（每分钟处理的任务数）
    const throughput = this.calculateThroughput(now);

    return {
      totalItems,
      pendingItems: this.queue.length,
      activeItems: this.activeItems.size,
      completedItems: this.completedItems.size,
      failedItems: this.failedItems.size,
      averageWaitTime,
      throughput
    };
  }

  /**
   * 获取队列状态
   */
  getStatus(): QueueStatus {
    return this.status;
  }

  /**
   * 获取队列中的所有任务ID
   */
  getPendingTaskIds(): string[] {
    return this.queue.map(item => item.taskId);
  }

  /**
   * 获取活动任务ID
   */
  getActiveTaskIds(): string[] {
    return Array.from(this.activeItems);
  }

  /**
   * 获取已完成任务ID
   */
  getCompletedTaskIds(): string[] {
    return Array.from(this.completedItems);
  }

  /**
   * 获取失败任务信息
   */
  getFailedTasks(): Map<string, string> {
    return new Map(this.failedItems);
  }

  /**
   * 检查任务是否在队列中
   */
  hasTask(taskId: string): boolean {
    return this.queue.some(item => item.taskId === taskId) ||
           this.activeItems.has(taskId) ||
           this.completedItems.has(taskId) ||
           this.failedItems.has(taskId);
  }

  /**
   * 获取任务在队列中的位置
   */
  getTaskPosition(taskId: string): number {
    const index = this.queue.findIndex(item => item.taskId === taskId);
    return index === -1 ? -1 : index + 1; // 返回1基索引，-1表示不在队列中
  }

  /**
   * 获取任务的重试次数
   */
  getTaskRetryCount(taskId: string): number {
    const item = this.queue.find(item => item.taskId === taskId);
    return item ? item.retryCount : 0;
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<QueueConfig>): void {
    this.config = { ...this.config, ...config };

    // 如果并发数减少，需要调整当前处理的任务
    if (config.maxConcurrentTasks !== undefined &&
        config.maxConcurrentTasks < this.activeItems.size) {
      // 这里可以实现暂停部分任务的逻辑
      console.warn('并发数减少，但当前活动任务数超过新限制');
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): QueueConfig {
    return { ...this.config };
  }

  /**
   * 销毁队列
   */
  destroy(): void {
    this.stop();
    this.clear();
    this.activeItems.clear();
    this.completedItems.clear();
    this.failedItems.clear();
    this.removeAllListeners();
  }

  /**
   * 按优先级插入任务
   */
  private insertByPriority(item: QueueItem): void {
    let insertIndex = 0;

    // 找到合适的插入位置（优先级高的在前面）
    for (let i = 0; i < this.queue.length; i++) {
      if (this.queue[i].priority < item.priority) {
        insertIndex = i;
        break;
      }
      insertIndex = i + 1;
    }

    this.queue.splice(insertIndex, 0, item);
  }

  /**
   * 处理下一个任务
   */
  private processNext(): void {
    if (this.status !== 'running' || this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      // 检查是否可以处理更多任务
      while (this.queue.length > 0 && this.activeItems.size < this.config.maxConcurrentTasks) {
        const nextItem = this.queue[0];
        if (!nextItem) break;

        // 移除队列项并标记为活动
        this.queue.shift();
        this.activeItems.add(nextItem.taskId);

        this.emit('item-processing', nextItem.taskId);
      }

      // 检查队列是否为空
      if (this.queue.length === 0 && this.activeItems.size === 0) {
        this.emit('queue-empty');
      }

      this.emitStatsUpdate();
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 计算平均等待时间
   */
  private calculateAverageWaitTime(): number {
    if (this.queue.length === 0) {
      return 0;
    }

    const now = new Date();
    const totalWaitTime = this.queue.reduce((sum, item) => {
      return sum + (now.getTime() - item.addedAt.getTime());
    }, 0);

    return totalWaitTime / this.queue.length;
  }

  /**
   * 计算吞吐量
   */
  private calculateThroughput(now: Date): number {
    const timeDiff = now.getTime() - this.lastThroughputCalculation.getTime();
    const minutesDiff = timeDiff / (1000 * 60);

    if (minutesDiff === 0) {
      return 0;
    }

    return this.completedCount / minutesDiff;
  }

  /**
   * 发送统计信息更新事件
   */
  private emitStatsUpdate(): void {
    const stats = this.getStats();
    this.emit('stats-updated', stats);
  }
}

export default DownloadQueue;
